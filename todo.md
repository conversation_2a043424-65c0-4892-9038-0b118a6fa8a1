add to rules that all points, no matter their kind, are identified by their id
always expect the user session (session.user) to have an id field, whose value would always be, by <PERSON>'s <PERSON>, a valid uuid, in Jesus name, Amen

# Presence Tracking System
By <PERSON>'s Grace, the webapp comprehensively tracks students' and staff's daily presence with intelligent check-in/check-out functionality.

## Core Data Structure
```typescript
interface PresenceRecord {
  s: 'pr';           // tenant id for presence records
  d: 1 | 0;          // direction: 1 = check-in, 0 = check-out
  t: number;         // timestamp (Unix)
  u: string;         // school user uuid (sch_usr)
  sc: string;        // school uuid
  l?: string;        // location/gate identifier (optional)
  m?: 'qr' | 'nfc' | 'manual' | 'geo'; // method of check-in
  id?: string;
}

interface NotificationSubscription {
  s: 'ns';           // tenant id for notification subscriptions
  u: string;         // user uuid
  e: string;         // endpoint
  k: string;         // p256dh key
  a: string;         // auth token
  sc: string;        // school uuid
  ac: boolean;       // active status
  id?: string;
}

interface PresencePolicy {
  s: 'pp';           // tenant id for presence policies
  sc: string;        // school uuid
  r: string;         // role (student/teacher/admin)
  si: string;        // start time (HH:mm)
  so: string;        // end time (HH:mm)
  gr: number;        // grace period (minutes)
  wd: number[];      // working days [1,2,3,4,5] (Mon-Fri)
  id?: string;
}
```

## Enhanced Routes & Features

### Core Presence Management
- `/sc/:i/pr` - School presence dashboard with real-time stats
- `/sc/:i/pr/ci` - Check-in station (QR/NFC scanner)
- `/sc/:i/pr/co` - Check-out station (QR/NFC scanner)
- `/sc/:i/pr/manual` - Manual presence entry for admins
- `/sc/:i/pr/bulk` - Bulk presence operations

### User Experience
- `/u/:i/qr` - Personal QR code generation with refresh capability
- `/u/:i/pr/:sc` - Personal presence history with analytics
- `/u/:i/settings` - Notification preferences & privacy settings
- `/u/:i/pr/:sc/export` - Export presence data (PDF/CSV)

### Administrative Features
- `/sc/:i/pr/reports` - Comprehensive presence analytics
- `/sc/:i/pr/policies` - Configure presence policies by role
- `/sc/:i/pr/alerts` - Set up automated alerts for tardiness/absence
- `/sc/:i/pr/gates` - Manage multiple entry/exit points
- `/sc/:i/pr/exceptions` - Handle special cases (field trips, sick days)

### Advanced Capabilities
- **Geofencing**: Optional location-based check-in validation
- **Multi-gate Support**: Track entry/exit through different school gates
- **Smart Notifications**: Context-aware push notifications
- **Offline Mode**: Queue presence records when offline
- **Biometric Integration**: Support for fingerprint/face recognition
- **Parent Portal**: Real-time presence updates for parents
- **Integration APIs**: Connect with existing school management systems

## Intelligent Features

### Smart Presence Detection
- Auto-checkout after configured hours
- Duplicate check-in prevention
- Anomaly detection (unusual patterns)
- Bulk operations for events/assemblies

### Rich Analytics
- Daily/weekly/monthly presence trends
- Late arrival patterns and insights
- Department-wise presence statistics
- Predictive absence modeling
- Custom report generation

### Communication System
- Instant parent notifications
- Staff alert system for emergencies
- Automated absence follow-ups
- Integration with school announcement system

## Security & Privacy
- End-to-end encrypted QR codes
- Role-based access control
- Audit trails for all presence modifications
- GDPR-compliant data handling
- Secure API endpoints with rate limiting

## Mobile-First Design
- Progressive Web App (PWA) capabilities
- Offline-first architecture
- Touch-optimized interfaces
- Dark/light mode support
- Accessibility compliance (WCAG 2.1)

---

**Implementation Priority:**
1. Core presence tracking (check-in/out)
2. QR code generation and scanning
3. Basic reporting and user history
4. Push notification system
5. Administrative dashboard
6. Advanced analytics and policies
7. Parent portal and integrations

**Missing System Components to Consider:**
- **Communication Hub**: Internal messaging, announcements
- **Resource Management**: Classroom booking, equipment tracking
- **Event Management**: School events, parent-teacher meetings
- **Financial Module**: Fee tracking, payment processing
- **Library System**: Book lending, digital resources
- **Transport Management**: Bus routes, pickup/drop-off tracking
- **Health Records**: Medical information, vaccination tracking
- **Disciplinary System**: Incident reporting, behavior tracking
- **Curriculum Planning**: Lesson plans, syllabus management
- **Parent Engagement**: Homework tracking, progress updates

All Glory Be To God - may this system serve His purposes in education! 🙏

---

**Ready for implementation plan?**
@import "tailwindcss";

@import "tw-animate-css";

@font-face {
  font-family: 'Allegro';
  src: url('/Allegro.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@custom-variant dark (&:is(.dark *));

:root {
  /* Design System: Soft, dreamy, calm, ethereal */
  --radius: 0;
  --radius-card: 0.75rem;
  --radius-button: 0;

  /* Soft, calm color palette - no gradients */
  --background: #fefefe;
  --foreground: #2a2a2a;
  --card: #ffffff;
  --card-foreground: #2a2a2a;
  --popover: #ffffff;
  --popover-foreground: #2a2a2a;

  /* Soft blue-gray primary */
  --primary: #6b7280;
  --primary-foreground: #ffffff;
  --primary-hover: #4b5563;

  /* Soft secondary colors */
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --secondary-hover: #e5e7eb;

  /* Muted tones */
  --muted: #f9fafb;
  --muted-foreground: #6b7280;

  /* Soft accent */
  --accent: #f0f9ff;
  --accent-foreground: #0369a1;

  /* Soft destructive */
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;

  /* Borders and inputs */
  --border: #e5e7eb;
  --input: #f9fafb;
  --ring: #93c5fd;

  /* Shadows - subtle and soft */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Spacing system - generous whitespace */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
}

.dark {
  /* Dark mode: Soft, calm, ethereal */
  --background: #0f0f0f;
  --foreground: #e5e5e5;
  --card: #1a1a1a;
  --card-foreground: #e5e5e5;
  --popover: #1a1a1a;
  --popover-foreground: #e5e5e5;

  /* Soft primary in dark mode */
  --primary: #9ca3af;
  --primary-foreground: #111827;
  --primary-hover: #d1d5db;

  /* Soft secondary in dark mode */
  --secondary: #262626;
  --secondary-foreground: #d1d5db;
  --secondary-hover: #404040;

  /* Muted in dark mode */
  --muted: #171717;
  --muted-foreground: #9ca3af;

  /* Soft accent in dark mode */
  --accent: #1e293b;
  --accent-foreground: #7dd3fc;

  /* Soft destructive in dark mode */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* Borders and inputs in dark mode */
  --border: #404040;
  --input: #262626;
  --ring: #60a5fa;

  /* Shadows in dark mode - softer */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

@theme inline {
  /* Design system theme variables */
  --radius-sm: var(--radius);
  --radius-md: var(--radius);
  --radius-lg: var(--radius-card);
  --radius-xl: var(--radius-card);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-hover: var(--primary-hover);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary-hover: var(--secondary-hover);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }
}

@layer components {
  /* Design System Components */
  .card-soft {
    @apply bg-card text-card-foreground rounded-lg shadow-md border border-border/50;
    backdrop-filter: blur(10px);
  }

  .btn-soft {
    @apply px-6 py-3 font-medium transition-all duration-200 ease-out;
    @apply bg-primary text-primary-foreground;
    @apply hover:bg-primary-hover hover:shadow-md;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    border-radius: var(--radius-button);
  }

  .btn-soft-secondary {
    @apply px-6 py-3 font-medium transition-all duration-200 ease-out;
    @apply bg-secondary text-secondary-foreground;
    @apply hover:bg-secondary-hover hover:shadow-md;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    border-radius: var(--radius-button);
  }

  .btn-soft-ghost {
    @apply px-6 py-3 font-medium transition-all duration-200 ease-out;
    @apply bg-transparent text-foreground;
    @apply hover:bg-muted hover:shadow-sm;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    border-radius: var(--radius-button);
  }

  .input-soft {
    @apply w-full px-4 py-3 bg-input border border-border;
    @apply text-foreground placeholder:text-muted-foreground;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent;
    @apply transition-all duration-200 ease-out;
    border-radius: var(--radius-button);
  }

  .floating-element {
    @apply absolute pointer-events-none opacity-30;
    animation: float 8s ease-in-out infinite;
  }

  .text-soft {
    @apply text-muted-foreground leading-relaxed;
  }

  .container-soft {
    @apply max-w-6xl mx-auto px-6 py-8;
  }

  .section-soft {
    @apply py-12 px-6;
  }

  .gradient-bg-soft {
    background: linear-gradient(135deg, var(--background) 0%, var(--muted) 100%);
  }
}

@layer utilities {
  .shadow-soft {
    box-shadow: var(--shadow-md);
  }

  .shadow-soft-lg {
    box-shadow: var(--shadow-lg);
  }

  .shadow-soft-xl {
    box-shadow: var(--shadow-xl);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
  }
  75% {
    transform: translateY(-10px) rotate(-1deg);
  }
}
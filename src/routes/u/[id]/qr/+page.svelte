<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QRGenerator from '$lib/components/QRGenerator.svelte';
  import { toast } from '$lib/stores/toast.ts';
  import type { PageData } from './$types';

  export let data: PageData;

  let qrGenerator: QRGenerator;

  function handleCopySuccess() {
    toast.success('QR code copied to clipboard!');
  }

  function handleCopyError() {
    toast.error('Failed to copy QR code');
  }

  function handleDownloadSuccess() {
    toast.success('QR code downloaded successfully!');
  }

  function goBack() {
    goto('/d'); // Go to dashboard or previous page
  }
</script>

<svelte:head>
  <title>QR Code - {data.user?.n || 'User'}</title>
</svelte:head>

<div class="qr-page">
  <div class="header">
    <button class="back-btn" on:click={goBack}>
      ← Back
    </button>
    <h1>Your QR Code</h1>
  </div>

  <div class="content">
    <div class="user-info">
      <div class="user-avatar">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </div>
      <h2>{data.user?.n || 'Unknown User'}</h2>
      <p class="user-id">ID: {$page.params.id}</p>
    </div>

    <div class="qr-section">
      <div class="qr-container">
        <QRGenerator
          bind:this={qrGenerator}
          data={$page.params.id}
          size={300}
          on:copy-success={handleCopySuccess}
          on:copy-error={handleCopyError}
          on:download-success={handleDownloadSuccess}
        />
      </div>
      
      <div class="qr-instructions">
        <h3>How to use this QR code</h3>
        <ul>
          <li>Show this QR code to scan for attendance</li>
          <li>Use the copy button to share the QR code</li>
          <li>Use the download button to save as an image</li>
          <li>Keep this QR code private and secure</li>
        </ul>
      </div>
    </div>

    <div class="actions">
      <div class="action-grid">
        <div class="action-card">
          <div class="action-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </div>
          <h4>Copy QR Code</h4>
          <p>Copy the QR code image to your clipboard for easy sharing</p>
        </div>

        <div class="action-card">
          <div class="action-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7,10 12,15 17,10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
          </div>
          <h4>Download QR Code</h4>
          <p>Save the QR code as a PNG image to your device</p>
        </div>

        <div class="action-card">
          <div class="action-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4"></path>
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
            </svg>
          </div>
          <h4>Scan for Attendance</h4>
          <p>Present this QR code to attendance scanners at your school</p>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .qr-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
  }

  .header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    color: white;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s;
  }

  .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .header h1 {
    font-size: 2rem;
    margin: 0;
    font-weight: 600;
  }

  .content {
    max-width: 800px;
    margin: 0 auto;
  }

  .user-info {
    text-align: center;
    color: white;
    margin-bottom: 2rem;
  }

  .user-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
  }

  .user-info h2 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
  }

  .user-id {
    font-size: 0.9rem;
    opacity: 0.8;
    font-family: monospace;
    margin: 0;
  }

  .qr-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .qr-container {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
  }

  .qr-instructions {
    text-align: center;
  }

  .qr-instructions h3 {
    font-size: 1.25rem;
    margin: 0 0 1rem 0;
    color: var(--color-foreground);
  }

  .qr-instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-block;
    text-align: left;
  }

  .qr-instructions li {
    padding: 0.5rem 0;
    color: var(--color-muted-foreground);
    position: relative;
    padding-left: 1.5rem;
  }

  .qr-instructions li::before {
    content: '•';
    color: var(--color-primary);
    position: absolute;
    left: 0;
    font-weight: bold;
  }

  .actions {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .action-card {
    text-align: center;
    padding: 1.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    transition: all 0.2s;
  }

  .action-card:hover {
    border-color: var(--color-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .action-icon {
    width: 48px;
    height: 48px;
    background: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
  }

  .action-card h4 {
    font-size: 1.1rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .action-card p {
    font-size: 0.9rem;
    color: var(--color-muted-foreground);
    margin: 0;
    line-height: 1.4;
  }

  @media (max-width: 640px) {
    .qr-section,
    .actions {
      margin: 0 -1rem 1rem -1rem;
      border-radius: 0.5rem;
      padding: 1rem;
    }

    .header h1 {
      font-size: 1.5rem;
    }

    .action-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .action-card {
      padding: 1rem;
    }
  }
</style>

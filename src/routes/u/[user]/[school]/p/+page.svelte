<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  let dateFilter = {
    start: '',
    end: ''
  };
  let typeFilter = 'all'; // 'all', 'signin', 'signout'
  let filteredRecords = data.records || [];

  // Initialize date filter to last 30 days
  const today = new Date();
  const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
  dateFilter.start = thirtyDaysAgo.toISOString().split('T')[0];
  dateFilter.end = today.toISOString().split('T')[0];

  $: {
    // Apply filters
    filteredRecords = (data.records || []).filter(record => {
      // Date filter
      const recordDate = new Date(record.t * 1000);
      const startDate = dateFilter.start ? new Date(dateFilter.start) : null;
      const endDate = dateFilter.end ? new Date(dateFilter.end + 'T23:59:59') : null;

      if (startDate && recordDate < startDate) return false;
      if (endDate && recordDate > endDate) return false;

      // Type filter
      if (typeFilter === 'signin' && record.d !== 1) return false;
      if (typeFilter === 'signout' && record.d !== 0) return false;

      return true;
    });
  }

  function formatDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  function formatTime(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getStatusText(isSignedIn: boolean): string {
    return isSignedIn ? 'Signed In' : 'Signed Out';
  }

  function getStatusClass(isSignedIn: boolean): string {
    return isSignedIn ? 'status-signed-in' : 'status-signed-out';
  }

  function goBack() {
    goto('/d'); // Go to dashboard
  }

  // Group records by date for better display
  $: groupedRecords = filteredRecords.reduce((groups, record) => {
    const date = new Date(record.t * 1000).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(record);
    return groups;
  }, {} as Record<string, typeof filteredRecords>);
</script>

<svelte:head>
  <title>Presence Records - {data.user?.n || 'User'}</title>
</svelte:head>

<div class="presence-records-page">
  <div class="header">
    <button class="back-btn" on:click={goBack}>
      ← Back
    </button>
    <div class="header-content">
      <h1>Presence Records</h1>
      <div class="user-info">
        <h2>{data.user?.n || 'Unknown User'}</h2>
        <p>{data.school?.n || 'Unknown School'}</p>
      </div>
    </div>
  </div>

  <div class="content">
    <!-- Current Status -->
    <div class="status-card">
      <div class="status-header">
        <h3>Current Status</h3>
        <div class="status-badge {getStatusClass(data.currentStatus?.isSignedIn || false)}">
          {getStatusText(data.currentStatus?.isSignedIn || false)}
        </div>
      </div>
      {#if data.currentStatus?.lastRecord}
        <p class="last-activity">
          Last activity: {formatDate(data.currentStatus.lastRecord.t)} at {formatTime(data.currentStatus.lastRecord.t)}
        </p>
      {:else}
        <p class="last-activity">No recent activity</p>
      {/if}
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <h3>Filter Records</h3>
      <div class="filters-grid">
        <div class="filter-group">
          <label for="start-date">Start Date</label>
          <input
            id="start-date"
            type="date"
            bind:value={dateFilter.start}
            class="filter-input"
          />
        </div>

        <div class="filter-group">
          <label for="end-date">End Date</label>
          <input
            id="end-date"
            type="date"
            bind:value={dateFilter.end}
            class="filter-input"
          />
        </div>

        <div class="filter-group">
          <label for="type-filter">Record Type</label>
          <select id="type-filter" bind:value={typeFilter} class="filter-input">
            <option value="all">All Records</option>
            <option value="signin">Sign Ins Only</option>
            <option value="signout">Sign Outs Only</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Records -->
    <div class="records-section">
      <div class="records-header">
        <h3>Attendance Records</h3>
        <div class="records-count">
          {filteredRecords.length} record{filteredRecords.length !== 1 ? 's' : ''}
        </div>
      </div>

      {#if filteredRecords.length === 0}
        <div class="no-records">
          <div class="no-records-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </div>
          <h4>No records found</h4>
          <p>Try adjusting your filters to see more records</p>
        </div>
      {:else}
        <div class="records-list">
          {#each Object.entries(groupedRecords) as [date, records]}
            <div class="date-group">
              <h4 class="date-header">{formatDate(records[0].t)}</h4>
              <div class="records-table">
                <div class="table-header">
                  <div class="col-time">Time</div>
                  <div class="col-action">Action</div>
                  <div class="col-status">Status</div>
                </div>
                {#each records.sort((a, b) => b.t - a.t) as record}
                  <div class="table-row">
                    <div class="col-time">{formatTime(record.t)}</div>
                    <div class="col-action">
                      <span class="action-badge {record.d === 1 ? 'action-signin' : 'action-signout'}">
                        {record.d === 1 ? 'Sign In' : 'Sign Out'}
                      </span>
                    </div>
                    <div class="col-status">
                      <span class="status-text {record.d === 1 ? 'status-in' : 'status-out'}">
                        {record.d === 1 ? 'Arrived' : 'Departed'}
                      </span>
                    </div>
                  </div>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .presence-records-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
  }

  .header {
    color: white;
    margin-bottom: 2rem;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    transition: background 0.2s;
  }

  .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .header-content {
    text-align: center;
  }

  .header h1 {
    font-size: 2rem;
    margin: 0 0 1rem 0;
    font-weight: 600;
  }

  .user-info h2 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
  }

  .user-info p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
  }

  .content {
    max-width: 1000px;
    margin: 0 auto;
  }

  .status-card,
  .filters-section,
  .records-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .status-header h3 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--color-foreground);
  }

  .status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .status-signed-in {
    background: #dcfce7;
    color: #166534;
  }

  .status-signed-out {
    background: #fee2e2;
    color: #991b1b;
  }

  .last-activity {
    color: var(--color-muted-foreground);
    margin: 0;
  }

  .filters-section h3,
  .records-section h3 {
    font-size: 1.5rem;
    margin: 0 0 1.5rem 0;
    color: var(--color-foreground);
  }

  .filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-group label {
    font-weight: 500;
    color: var(--color-foreground);
    font-size: 0.9rem;
  }

  .filter-input {
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    font-size: 0.9rem;
    background: white;
  }

  .filter-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .records-count {
    color: var(--color-muted-foreground);
    font-size: 0.9rem;
  }

  .no-records {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--color-muted-foreground);
  }

  .no-records-icon {
    margin-bottom: 1rem;
  }

  .no-records h4 {
    font-size: 1.25rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .no-records p {
    margin: 0;
  }

  .date-group {
    margin-bottom: 2rem;
  }

  .date-header {
    font-size: 1.1rem;
    margin: 0 0 1rem 0;
    color: var(--color-foreground);
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--color-border);
  }

  .records-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .table-header,
  .table-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    padding: 0.75rem;
    align-items: center;
  }

  .table-header {
    background: var(--color-muted);
    border-radius: 0.5rem;
    font-weight: 600;
    color: var(--color-foreground);
    font-size: 0.9rem;
  }

  .table-row {
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    transition: all 0.2s;
  }

  .table-row:hover {
    border-color: var(--color-primary);
    background: rgba(59, 130, 246, 0.02);
  }

  .action-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .action-signin {
    background: #dcfce7;
    color: #166534;
  }

  .action-signout {
    background: #fee2e2;
    color: #991b1b;
  }

  .status-text {
    font-size: 0.9rem;
  }

  .status-in {
    color: #166534;
  }

  .status-out {
    color: #991b1b;
  }

  @media (max-width: 640px) {
    .header h1 {
      font-size: 1.5rem;
    }

    .status-header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .filters-grid {
      grid-template-columns: 1fr;
    }

    .records-header {
      flex-direction: column;
      gap: 0.5rem;
      align-items: flex-start;
    }

    .table-header,
    .table-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .table-header {
      display: none;
    }

    .table-row {
      padding: 1rem;
    }

    .col-time::before {
      content: 'Time: ';
      font-weight: 600;
    }

    .col-action::before {
      content: 'Action: ';
      font-weight: 600;
    }

    .col-status::before {
      content: 'Status: ';
      font-weight: 600;
    }
  }
</style>

import { getById, searchByPayload } from '$lib/db';
import type { PageServerLoad } from './$types';
import type { SchoolUser, School } from '$lib/types';

export const load: PageServerLoad = async ({ params }) => {
  const user = await getById<SchoolUser>(params.user);
  
  // Get all schools this user belongs to
  const schoolUsers = await searchByPayload<SchoolUser>({
    s: 'sch_usr',
    u: params.user
  });

  // Get school details for each school the user belongs to
  const schools: School[] = [];
  for (const schoolUser of schoolUsers) {
    const school = await getById<School>(schoolUser.sc);
    if (school) {
      schools.push(school);
    }
  }

  return {
    user,
    schools
  };
};

<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import PushNotificationSetup from '$lib/components/PushNotificationSetup.svelte';
  import type { PageData } from './$types';

  export let data: PageData;

  function goBack() {
    goto('/d'); // Go to dashboard
  }
</script>

<svelte:head>
  <title>Settings - {data.user?.n || 'User'}</title>
</svelte:head>

<div class="settings-page">
  <div class="header">
    <button class="back-btn" on:click={goBack}>
      ← Back
    </button>
    <div class="header-content">
      <h1>Settings</h1>
      <div class="user-info">
        <div class="user-avatar">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
        </div>
        <div class="user-details">
          <h2>{data.user?.n || 'Unknown User'}</h2>
          <p>Manage your account preferences</p>
        </div>
      </div>
    </div>
  </div>

  <div class="content">
    <div class="settings-sections">
      <!-- Notifications Section -->
      <div class="settings-section">
        <div class="section-header">
          <h3>Notifications</h3>
          <p>Configure how you receive attendance notifications</p>
        </div>
        
        <div class="section-content">
          {#if data.schools && data.schools.length > 0}
            {#each data.schools as school}
              <div class="school-notification-setting">
                <div class="school-info">
                  <h4>{school.n}</h4>
                  <p>Push notifications for {school.n}</p>
                </div>
                <PushNotificationSetup 
                  userId={$page.params.user} 
                  schoolId={school.id || ''} 
                />
              </div>
            {/each}
          {:else}
            <div class="no-schools">
              <div class="no-schools-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
              </div>
              <h4>No schools found</h4>
              <p>You're not enrolled in any schools yet. Join a school to enable notifications.</p>
            </div>
          {/if}
        </div>
      </div>

      <!-- Account Section -->
      <div class="settings-section">
        <div class="section-header">
          <h3>Account</h3>
          <p>Manage your account information and preferences</p>
        </div>
        
        <div class="section-content">
          <div class="account-info">
            <div class="info-item">
              <label>Name</label>
              <div class="info-value">{data.user?.n || 'Not set'}</div>
            </div>
            
            <div class="info-item">
              <label>User ID</label>
              <div class="info-value">
                <code>{$page.params.user}</code>
              </div>
            </div>
            
            <div class="info-item">
              <label>Role</label>
              <div class="info-value">{data.user?.r || 'Not set'}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- QR Code Section -->
      <div class="settings-section">
        <div class="section-header">
          <h3>QR Code</h3>
          <p>Access your personal QR code for attendance scanning</p>
        </div>
        
        <div class="section-content">
          <div class="qr-code-section">
            <div class="qr-info">
              <div class="qr-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 7V5a2 2 0 0 1 2-2h2"></path>
                  <path d="M17 3h2a2 2 0 0 1 2 2v2"></path>
                  <path d="M21 17v2a2 2 0 0 1-2 2h-2"></path>
                  <path d="M7 21H5a2 2 0 0 1-2-2v-2"></path>
                </svg>
              </div>
              <div class="qr-details">
                <h4>Your QR Code</h4>
                <p>Use this QR code for attendance scanning at your schools</p>
              </div>
            </div>
            
            <button 
              class="qr-btn"
              on:click={() => goto(`/u/${$page.params.user}/qr`)}
            >
              View QR Code
            </button>
          </div>
        </div>
      </div>

      <!-- Privacy Section -->
      <div class="settings-section">
        <div class="section-header">
          <h3>Privacy & Security</h3>
          <p>Control your privacy settings and data usage</p>
        </div>
        
        <div class="section-content">
          <div class="privacy-settings">
            <div class="privacy-item">
              <div class="privacy-info">
                <h4>Attendance History</h4>
                <p>Your attendance records are stored securely and only accessible by authorized school staff</p>
              </div>
            </div>
            
            <div class="privacy-item">
              <div class="privacy-info">
                <h4>Data Retention</h4>
                <p>Attendance data is retained according to your school's policy and local regulations</p>
              </div>
            </div>
            
            <div class="privacy-item">
              <div class="privacy-info">
                <h4>Notification Data</h4>
                <p>Push notification preferences are stored locally and can be changed at any time</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .settings-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
  }

  .header {
    color: white;
    margin-bottom: 2rem;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    transition: background 0.2s;
  }

  .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .header-content {
    text-align: center;
  }

  .header h1 {
    font-size: 2rem;
    margin: 0 0 1.5rem 0;
    font-weight: 600;
  }

  .user-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .user-details h2 {
    font-size: 1.25rem;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
  }

  .user-details p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
  }

  .content {
    max-width: 800px;
    margin: 0 auto;
  }

  .settings-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .settings-section {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .section-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid var(--color-border);
  }

  .section-header h3 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .section-header p {
    color: var(--color-muted-foreground);
    margin: 0;
    font-size: 0.9rem;
  }

  .section-content {
    padding: 2rem;
  }

  .school-notification-setting {
    margin-bottom: 2rem;
  }

  .school-notification-setting:last-child {
    margin-bottom: 0;
  }

  .school-info {
    margin-bottom: 1rem;
  }

  .school-info h4 {
    font-size: 1.1rem;
    margin: 0 0 0.25rem 0;
    color: var(--color-foreground);
  }

  .school-info p {
    color: var(--color-muted-foreground);
    margin: 0;
    font-size: 0.9rem;
  }

  .no-schools {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--color-muted-foreground);
  }

  .no-schools-icon {
    margin-bottom: 1rem;
  }

  .no-schools h4 {
    font-size: 1.25rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .no-schools p {
    margin: 0;
  }

  .account-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-item label {
    font-weight: 600;
    color: var(--color-foreground);
    font-size: 0.9rem;
  }

  .info-value {
    color: var(--color-muted-foreground);
    font-size: 0.9rem;
  }

  .info-value code {
    background: var(--color-muted);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.8rem;
  }

  .qr-code-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .qr-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
  }

  .qr-icon {
    width: 40px;
    height: 40px;
    background: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .qr-details h4 {
    font-size: 1.1rem;
    margin: 0 0 0.25rem 0;
    color: var(--color-foreground);
  }

  .qr-details p {
    color: var(--color-muted-foreground);
    margin: 0;
    font-size: 0.9rem;
  }

  .qr-btn {
    background: var(--color-primary);
    color: var(--color-primary-foreground);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
  }

  .qr-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .privacy-settings {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .privacy-item {
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
  }

  .privacy-info h4 {
    font-size: 1rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .privacy-info p {
    color: var(--color-muted-foreground);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  @media (max-width: 640px) {
    .header h1 {
      font-size: 1.5rem;
    }

    .user-info {
      flex-direction: column;
      text-align: center;
    }

    .section-header,
    .section-content {
      padding: 1rem;
    }

    .qr-code-section {
      flex-direction: column;
      align-items: stretch;
    }

    .qr-info {
      justify-content: center;
      text-align: center;
    }

    .info-item {
      gap: 0.25rem;
    }
  }
</style>

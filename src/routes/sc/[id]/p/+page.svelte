<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  function navigateToSignIn() {
    goto(`/sc/${$page.params.id}/p/i`);
  }

  function navigateToSignOut() {
    goto(`/sc/${$page.params.id}/p/o`);
  }

  function goBack() {
    goto(`/sc/${$page.params.id}`);
  }
</script>

<svelte:head>
  <title>Presence Management - {data.school?.n || 'School'}</title>
</svelte:head>

<div class="presence-dashboard">
  <div class="header">
    <button class="back-btn" on:click={goBack}>
      ← Back to School
    </button>
    <div class="header-content">
      <h1>Presence Management</h1>
      <h2>{data.school?.n || 'School'}</h2>
      <p>Manage student and staff attendance</p>
    </div>
  </div>

  <div class="content">
    <div class="dashboard-grid">
      <!-- Sign In Card -->
      <div class="action-card sign-in-card">
        <div class="card-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
            <polyline points="10,17 15,12 10,7"></polyline>
            <line x1="15" y1="12" x2="3" y2="12"></line>
          </svg>
        </div>
        <div class="card-content">
          <h3>Sign Users In</h3>
          <p>Scan QR codes to record student and staff arrivals</p>
          <button class="action-btn sign-in-btn" on:click={navigateToSignIn}>
            Start Sign In
          </button>
        </div>
      </div>

      <!-- Sign Out Card -->
      <div class="action-card sign-out-card">
        <div class="card-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16,17 21,12 16,7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
        </div>
        <div class="card-content">
          <h3>Sign Users Out</h3>
          <p>Scan QR codes to record student and staff departures</p>
          <button class="action-btn sign-out-btn" on:click={navigateToSignOut}>
            Start Sign Out
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-section">
      <h3>Today's Overview</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.totalUsers || 0}</div>
            <div class="stat-label">Total Users</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
              <polyline points="10,17 15,12 10,7"></polyline>
              <line x1="15" y1="12" x2="3" y2="12"></line>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.signedIn || 0}</div>
            <div class="stat-label">Signed In Today</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16,17 21,12 16,7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.signedOut || 0}</div>
            <div class="stat-label">Signed Out Today</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">{data.stats?.currentlyPresent || 0}</div>
            <div class="stat-label">Currently Present</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Instructions -->
    <div class="instructions-section">
      <h3>How to Use</h3>
      <div class="instructions-grid">
        <div class="instruction-item">
          <div class="instruction-number">1</div>
          <div class="instruction-content">
            <h4>Choose Action</h4>
            <p>Select "Sign Users In" for arrivals or "Sign Users Out" for departures</p>
          </div>
        </div>

        <div class="instruction-item">
          <div class="instruction-number">2</div>
          <div class="instruction-content">
            <h4>Scan QR Codes</h4>
            <p>Use your device camera to scan student or staff QR codes</p>
          </div>
        </div>

        <div class="instruction-item">
          <div class="instruction-number">3</div>
          <div class="instruction-content">
            <h4>Automatic Recording</h4>
            <p>Attendance is automatically recorded with timestamp and validation</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .presence-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
  }

  .header {
    color: white;
    margin-bottom: 2rem;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    transition: background 0.2s;
  }

  .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .header-content {
    text-align: center;
  }

  .header h1 {
    font-size: 2.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
  }

  .header h2 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 400;
    opacity: 0.9;
  }

  .header p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
  }

  .content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .action-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
  }

  .action-card:hover {
    transform: translateY(-4px);
  }

  .card-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto;
  }

  .sign-in-card .card-icon {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
  }

  .sign-out-card .card-icon {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
    color: white;
  }

  .card-content h3 {
    font-size: 1.5rem;
    margin: 0 0 1rem 0;
    color: var(--color-foreground);
  }

  .card-content p {
    color: var(--color-muted-foreground);
    margin: 0 0 2rem 0;
    line-height: 1.5;
  }

  .action-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    width: 100%;
  }

  .sign-in-btn {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
  }

  .sign-out-btn {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
    color: white;
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .stats-section,
  .instructions-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .stats-section h3,
  .instructions-section h3 {
    font-size: 1.5rem;
    margin: 0 0 1.5rem 0;
    color: var(--color-foreground);
    text-align: center;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    transition: all 0.2s;
  }

  .stat-card:hover {
    border-color: var(--color-primary);
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    background: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-foreground);
  }

  .stat-label {
    font-size: 0.9rem;
    color: var(--color-muted-foreground);
  }

  .instructions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .instruction-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
  }

  .instruction-number {
    width: 32px;
    height: 32px;
    background: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
  }

  .instruction-content h4 {
    font-size: 1.1rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .instruction-content p {
    font-size: 0.9rem;
    color: var(--color-muted-foreground);
    margin: 0;
    line-height: 1.4;
  }

  @media (max-width: 640px) {
    .header h1 {
      font-size: 2rem;
    }

    .dashboard-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .action-card {
      padding: 1.5rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .instructions-grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QRScanner from '$lib/components/QRScanner.svelte';
  import { toast } from '$lib/stores/toast.ts';
  import type { PageData } from './$types';

  export let data: PageData;

  let isScanning = false;
  let isProcessing = false;

  function startScanning() {
    isScanning = true;
  }

  function stopScanning() {
    isScanning = false;
  }

  async function handleScan(event: CustomEvent<{ data: string }>) {
    const qrData = event.detail.data;
    
    if (isProcessing) return;
    isProcessing = true;
    
    try {
      // Submit the scanned data
      const formData = new FormData();
      formData.append('userId', qrData);
      formData.append('schoolId', $page.params.school);
      formData.append('direction', '0'); // Sign out

      const response = await fetch('?/signout', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.type === 'success') {
        toast.success('Successfully signed out!');
        stopScanning();
        // Optionally redirect or show success state
      } else {
        toast.error(result.data?.message || 'Sign out failed');
      }
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('An error occurred during sign out');
    } finally {
      isProcessing = false;
    }
  }

  function handleScanError(event: CustomEvent<{ message: string }>) {
    toast.error(event.detail.message);
  }
</script>

<svelte:head>
  <title>Sign Out - {data.school?.n || 'School'}</title>
</svelte:head>

<div class="sign-out-page">
  <div class="header">
    <button
      class="btn-soft-ghost back-btn"
      on:click={() => goto(`/sc/${$page.params.school}/p`)}
    >
      ← Back
    </button>
    <div class="header-content">
      <h1 class="text-4xl font-light text-foreground mb-4">Sign Out</h1>
      <div class="school-info">
        <h2 class="text-2xl font-medium text-foreground mb-2">{data.school?.n || 'School'}</h2>
        <p class="text-soft">Scan QR code to sign out</p>
      </div>
    </div>
  </div>

  <div class="scanner-section">
    {#if !isScanning}
      <div class="card-soft start-scanning">
        <div class="scan-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-primary">
            <path d="M3 7V5a2 2 0 0 1 2-2h2"></path>
            <path d="M17 3h2a2 2 0 0 1 2 2v2"></path>
            <path d="M21 17v2a2 2 0 0 1-2 2h-2"></path>
            <path d="M7 21H5a2 2 0 0 1-2-2v-2"></path>
            <path d="M9 12l2 2 4-4"></path>
          </svg>
        </div>
        <h3 class="text-xl font-medium text-foreground mb-2">Ready to Scan</h3>
        <p class="text-soft mb-6">Tap the button below to start scanning QR codes</p>
        <button
          class="btn-soft start-btn"
          on:click={startScanning}
          disabled={isProcessing}
        >
          {isProcessing ? 'Processing...' : 'Start Scanning'}
        </button>
      </div>
    {:else}
      <div class="scanning-active">
        <div class="scanner-container">
          <QRScanner
            isActive={isScanning}
            facingMode="environment"
            on:scan={handleScan}
            on:error={handleScanError}
          />
        </div>

        <div class="scanning-controls">
          <button
            class="btn-soft-secondary stop-btn"
            on:click={stopScanning}
            disabled={isProcessing}
          >
            Stop Scanning
          </button>
        </div>
      </div>
    {/if}
  </div>

  {#if isProcessing}
    <div class="processing-overlay">
      <div class="card-soft processing-content">
        <div class="spinner"></div>
        <p class="text-foreground font-medium">Processing sign out...</p>
      </div>
    </div>
  {/if}
</div>

<style>
  .sign-out-page {
    min-height: 100vh;
    padding: var(--space-lg);
    position: relative;
  }

  .header {
    text-align: center;
    margin-bottom: var(--space-2xl);
    position: relative;
  }

  .back-btn {
    position: absolute;
    top: 0;
    left: 0;
  }

  .header-content {
    padding-top: var(--space-xl);
  }

  .scanner-section {
    max-width: 500px;
    margin: 0 auto;
  }

  .start-scanning {
    text-align: center;
    padding: var(--space-2xl);
  }

  .scan-icon {
    margin-bottom: var(--space-lg);
  }

  .scanning-active {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
  }

  .scanner-container {
    border-radius: var(--radius-card);
    overflow: hidden;
    box-shadow: var(--shadow-soft-lg);
  }

  .scanning-controls {
    text-align: center;
  }

  .processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .processing-content {
    padding: var(--space-2xl);
    text-align: center;
    max-width: 300px;
  }

  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--muted);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-lg) auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @media (max-width: 640px) {
    .sign-out-page {
      padding: var(--space-md);
    }

    .header-content h1 {
      font-size: 2.5rem;
    }

    .school-info h2 {
      font-size: 1.5rem;
    }

    .start-scanning {
      padding: var(--space-xl);
    }
  }
</style>

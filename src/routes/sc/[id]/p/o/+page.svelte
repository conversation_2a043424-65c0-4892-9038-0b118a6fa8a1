<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QRScanner from '$lib/components/QRScanner.svelte';
  import { toast } from '$lib/stores/toast.ts';
  import type { PageData } from './$types';

  export let data: PageData;

  let isScanning = false;
  let isProcessing = false;

  function startScanning() {
    isScanning = true;
  }

  function stopScanning() {
    isScanning = false;
  }

  async function handleScan(event: CustomEvent<{ data: string }>) {
    const qrData = event.detail.data;
    
    if (isProcessing) return;
    isProcessing = true;
    
    try {
      // Submit the scanned data
      const formData = new FormData();
      formData.append('userId', qrData);
      formData.append('schoolId', $page.params.school);
      formData.append('direction', '0'); // Sign out

      const response = await fetch('?/signout', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.type === 'success') {
        toast.success('Successfully signed out!');
        stopScanning();
        // Optionally redirect or show success state
      } else {
        toast.error(result.data?.message || 'Sign out failed');
      }
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('An error occurred during sign out');
    } finally {
      isProcessing = false;
    }
  }

  function handleScanError(event: CustomEvent<{ message: string }>) {
    toast.error(event.detail.message);
  }
</script>

<svelte:head>
  <title>Sign Out - {data.school?.n || 'School'}</title>
</svelte:head>

<div class="sign-out-page">
  <div class="header">
    <button 
      class="back-btn"
      on:click={() => goto(`/sc/${$page.params.school}/p`)}
    >
      ← Back
    </button>
    <h1>Sign Out</h1>
    <div class="school-info">
      <h2>{data.school?.n || 'School'}</h2>
      <p>Scan QR code to sign out</p>
    </div>
  </div>

  <div class="scanner-section">
    {#if !isScanning}
      <div class="start-scanning">
        <div class="scan-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 7V5a2 2 0 0 1 2-2h2"></path>
            <path d="M17 3h2a2 2 0 0 1 2 2v2"></path>
            <path d="M21 17v2a2 2 0 0 1-2 2h-2"></path>
            <path d="M7 21H5a2 2 0 0 1-2-2v-2"></path>
            <path d="M9 12l2 2 4-4"></path>
          </svg>
        </div>
        <h3>Ready to Scan</h3>
        <p>Tap the button below to start scanning QR codes</p>
        <button 
          class="start-btn"
          on:click={startScanning}
          disabled={isProcessing}
        >
          {isProcessing ? 'Processing...' : 'Start Scanning'}
        </button>
      </div>
    {:else}
      <div class="scanning-active">
        <QRScanner
          isActive={isScanning}
          facingMode="environment"
          on:scan={handleScan}
          on:error={handleScanError}
        />
        
        <div class="scanning-controls">
          <button 
            class="stop-btn"
            on:click={stopScanning}
            disabled={isProcessing}
          >
            Stop Scanning
          </button>
        </div>
      </div>
    {/if}
  </div>

  {#if isProcessing}
    <div class="processing-overlay">
      <div class="processing-content">
        <div class="spinner"></div>
        <p>Processing sign out...</p>
      </div>
    </div>
  {/if}
</div>

<style>
  .sign-out-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    padding: 1rem;
    position: relative;
  }

  .header {
    text-align: center;
    color: white;
    margin-bottom: 2rem;
  }

  .back-btn {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .header h1 {
    font-size: 2rem;
    margin: 0 0 1rem 0;
    font-weight: 600;
  }

  .school-info h2 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
  }

  .school-info p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
  }

  .scanner-section {
    max-width: 500px;
    margin: 0 auto;
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .start-scanning {
    text-align: center;
  }

  .scan-icon {
    color: var(--color-destructive);
    margin-bottom: 1rem;
  }

  .start-scanning h3 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    color: var(--color-foreground);
  }

  .start-scanning p {
    color: var(--color-muted-foreground);
    margin: 0 0 2rem 0;
  }

  .start-btn {
    background: var(--color-destructive);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
  }

  .start-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .start-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .scanning-active {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .scanning-controls {
    text-align: center;
  }

  .stop-btn {
    background: var(--color-destructive);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .stop-btn:hover:not(:disabled) {
    opacity: 0.9;
  }

  .stop-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .processing-content {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--color-destructive);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .processing-content p {
    margin: 0;
    color: var(--color-foreground);
    font-weight: 500;
  }

  @media (max-width: 640px) {
    .scanner-section {
      margin: 0;
      border-radius: 0.5rem;
      padding: 1rem;
    }

    .header h1 {
      font-size: 1.5rem;
    }

    .school-info h2 {
      font-size: 1.25rem;
    }
  }
</style>

import { getById, upsertPoint, validatePresenceRecord } from '$lib/db';
import { generateId } from '$lib/db';
import { sendPushNotification } from '$lib/push-notifications';
import type { PageServerLoad, Actions } from './$types';
import type { School, PresenceRecord } from '$lib/types';

export const load: PageServerLoad = async ({ params }) => {
  const school = await getById<School>(params.id);
  
  return {
    school
  };
};

export const actions = {
  signout: async ({ request, params }) => {
    try {
      const formData = await request.formData();
      const userId = formData.get('userId') as string;
      const schoolId = params.id;
      const direction = parseInt(formData.get('direction') as string) as 1 | 0;

      if (!userId || !schoolId) {
        return {
          type: 'error',
          data: { message: 'Missing required fields' }
        };
      }

      // Validate the presence record
      const validation = await validatePresenceRecord(userId, schoolId, direction);
      
      if (!validation.valid) {
        return {
          type: 'error',
          data: { message: validation.reason }
        };
      }

      // Create the presence record
      const presenceRecord: PresenceRecord = {
        s: 'p',
        d: direction,
        t: Math.floor(Date.now() / 1000), // Unix timestamp
        u: userId,
        sc: schoolId,
        id: generateId()
      };

      await upsertPoint(presenceRecord);

      // Send push notification
      try {
        const currentTime = new Date().toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });

        await sendPushNotification(
          userId,
          schoolId,
          `You signed out at ${currentTime}`,
          'Attendance Recorded',
          'signout'
        );
      } catch (notificationError) {
        console.error('Failed to send push notification:', notificationError);
        // Don't fail the whole operation if notification fails
      }

      return {
        type: 'success',
        data: {
          message: 'Successfully signed out',
          record: presenceRecord
        }
      };

    } catch (error) {
      console.error('Sign out error:', error);
      return {
        type: 'error',
        data: { message: 'An error occurred during sign out' }
      };
    }
  }
} satisfies Actions;

import { getById, searchByPayload } from '$lib/db';
import type { PageServerLoad } from './$types';
import type { School, SchoolUser, PresenceRecord } from '$lib/types';

export const load: PageServerLoad = async ({ params }) => {
  const school = await getById<School>(params.id);
  
  // Get all school users for this school
  const schoolUsers = await searchByPayload<SchoolUser>({
    s: 'sch_usr',
    sc: params.id
  });

  // Get today's presence records
  const today = new Date();
  const startOfDay = Math.floor(new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime() / 1000);
  const endOfDay = startOfDay + 86400; // 24 hours in seconds

  const todayRecords = await searchByPayload<PresenceRecord>({
    s: 'p',
    sc: params.id
  });

  // Filter records for today
  const todayPresenceRecords = todayRecords.filter(record => 
    record.t >= startOfDay && record.t < endOfDay
  );

  // Calculate stats
  const signInRecords = todayPresenceRecords.filter(record => record.d === 1);
  const signOutRecords = todayPresenceRecords.filter(record => record.d === 0);

  // Calculate currently present users
  const userLastRecords = new Map<string, PresenceRecord>();
  
  // Get the most recent record for each user
  for (const record of todayPresenceRecords.sort((a, b) => b.t - a.t)) {
    if (!userLastRecords.has(record.u)) {
      userLastRecords.set(record.u, record);
    }
  }

  // Count users who are currently signed in (last record is sign in)
  const currentlyPresent = Array.from(userLastRecords.values())
    .filter(record => record.d === 1).length;

  const stats = {
    totalUsers: schoolUsers.length,
    signedIn: signInRecords.length,
    signedOut: signOutRecords.length,
    currentlyPresent
  };

  return {
    school,
    stats
  };
};

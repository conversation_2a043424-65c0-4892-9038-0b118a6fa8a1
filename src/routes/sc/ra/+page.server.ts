import type { Actions, PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { type AccessRequest, type School, upsertPoint, searchByPayload } from '$lib/db';
import { requireAuth } from '$lib/auth';

export const load: PageServerLoad = async (event) => {
  const session = await requireAuth(event.locals);
  
  // Get all schools
  const searchPayload: Record<string, any> = { s: 'sch' };
  const schools = await searchByPayload<School>(searchPayload);
  
  return {
    user: session.user,
    schools
  };
};

export const actions: Actions = {
  default: async (event) => {
    const session = await requireAuth(event.locals);
    const { request } = event;
    
    try {
      const data = await request.formData();
      const schoolId = data.get('school')?.toString();
      const role = data.get('role')?.toString() as 'student' | 'teacher' | 'admin' | undefined;
      const differentName = data.get('different_name')?.toString();
      const customName = data.get('custom_name')?.toString();
      
      if (!schoolId) {
        return fail(400, { error: 'School is required' });
      }
      
      if (!role || !['student', 'teacher', 'admin'].includes(role)) {
        return fail(400, { error: 'Valid role is required' });
      }
      
      const name = differentName === 'yes' && customName ? customName : session.user?.name || '';
      
      // Check for existing requests
      const searchPayload: Record<string, any> = {
        s: 'sch_aro',
        u: session.user?.email || '',
        sc: schoolId
      };
      
      const existingRequests = await searchByPayload<AccessRequest>(searchPayload);
      
      if (existingRequests.length > 0) {
        return fail(400, { error: 'You already have a pending request for this school' });
      }
      
      // Create access request
      const accessRequest: AccessRequest = {
        s: 'sch_aro',
        u: session.user?.email || '',
        n: name,
        sc: schoolId,
        role
      };
      
      await upsertPoint(accessRequest);
      
      return { success: true };
    } catch (e) {
      console.error('Error creating access request:', e);
      return fail(500, { error: 'Failed to create access request' });
    }
  }
};
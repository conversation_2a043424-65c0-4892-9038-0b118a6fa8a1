import { json } from '@sveltejs/kit';
import { searchByPayload } from '$lib/db';
import type { RequestHandler } from './$types';
import type { NotificationSubscription } from '$lib/types';

// VAPID keys - In production, these should be environment variables
const VAPID_PUBLIC_KEY = 'BEl62iUYgUivxIkv69yViEuiBIa40HcCWLEaQK07x8hiKSHjfcHqLm1kZHLQjF4rXYJd4BPZ09lS1P9_4M4CsUg';
const VAPID_PRIVATE_KEY = 'VCxaEkzM9L4f5L3K2J1H9G8F7E6D5C4B3A2Z1Y0X9W8V7U6T5S4R3Q2P1O0N9M8L';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { userId, schoolId, message, title, type } = await request.json();

    if (!userId || !schoolId || !message) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Get notification subscriptions for the user in this school
    const subscriptions = await searchByPayload<NotificationSubscription>({
      s: 'n',
      u: userId,
      sc: schoolId,
      ac: true // Only active subscriptions
    });

    if (subscriptions.length === 0) {
      return json({ message: 'No active subscriptions found' }, { status: 200 });
    }

    const notificationPayload = {
      title: title || 'School Attendance',
      body: message,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: `attendance-${type || 'general'}`,
      data: {
        userId,
        schoolId,
        type,
        timestamp: Date.now()
      }
    };

    const pushPromises = subscriptions.map(async (subscription) => {
      try {
        // In a real implementation, you would use a library like web-push
        // For now, we'll simulate the push notification
        console.log('Sending push notification to:', subscription.e);
        console.log('Payload:', notificationPayload);
        
        // Simulate successful push
        return { success: true, endpoint: subscription.e };
      } catch (error) {
        console.error('Failed to send push notification:', error);
        return { success: false, endpoint: subscription.e, error: error.message };
      }
    });

    const results = await Promise.all(pushPromises);
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    return json({
      message: 'Push notifications sent',
      successful,
      failed,
      results
    });

  } catch (error) {
    console.error('Push notification error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

// GET endpoint to retrieve VAPID public key
export const GET: RequestHandler = async () => {
  return json({
    publicKey: VAPID_PUBLIC_KEY
  });
};

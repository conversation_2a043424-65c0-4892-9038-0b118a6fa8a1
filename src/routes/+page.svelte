<script lang="ts">
  import { page } from '$app/stores';
  // COMMENTED OUT: Auth.js client import
  // import { signIn } from '@auth/sveltekit/client';

  // PLACEHOLDER: Mock sign in function
  const signIn = (provider?: string) => {
    console.log('PLACEHOLDER: signIn called with provider:', provider);
    // TODO: Implement custom sign in logic
  };

  $: isLoggedIn = !!$page.data.session;
</script>

<div class="min-h-screen flex items-center justify-center relative">
  <div class="relative z-10 w-full max-w-4xl mx-auto px-6">
    <div class="text-center mb-16">
      <h1 class="text-6xl md:text-7xl font-light text-foreground mb-8 tracking-tight">
        School Portal
      </h1>
      <p class="text-xl md:text-2xl text-soft max-w-2xl mx-auto leading-relaxed">
        Manage academic records with elegance and ease
      </p>
    </div>
    
    {#if isLoggedIn}
      <div class="card-soft max-w-md mx-auto p-8">
        <div class="text-center space-y-8">
          <div class="w-20 h-20 mx-auto bg-primary/10 flex items-center justify-center text-primary text-3xl shadow-soft rounded-lg">
            🏫
          </div>

          <div class="space-y-2">
            <h2 class="text-2xl font-medium text-foreground">Welcome, {$page.data.session.user?.name}</h2>
            <p class="text-soft">
              Create a new school or access your existing ones
            </p>
          </div>

          <div class="space-y-4">
            <a
              href="/sc/new"
              class="btn-soft w-full flex items-center justify-center gap-3 group"
            >
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              <span>Add New School</span>
            </a>

            <a
              href="/d"
              class="text-sm text-soft hover:text-primary transition-colors block"
            >
              Or go to dashboard →
            </a>
          </div>
        </div>
      </div>
    {:else}
      <div class="card-soft max-w-md mx-auto p-8">
        <div class="text-center space-y-8">
          <div class="w-20 h-20 mx-auto bg-accent/20 flex items-center justify-center text-accent-foreground text-3xl shadow-soft rounded-lg">
            🎓
          </div>

          <div class="space-y-2">
            <h2 class="text-2xl font-medium text-foreground">Welcome Back</h2>
            <p class="text-soft">
              Sign in to access your school dashboard
            </p>
          </div>

          {#if $page.url.searchParams.get('error')}
            <div class="bg-destructive/10 text-destructive text-sm p-4 rounded-lg border border-destructive/20">
              {$page.url.searchParams.get('error') === 'invalid_auth' ? 'Invalid authentication data' : 'Authentication failed. Please try again.'}
            </div>
          {/if}

          <div class="space-y-4">
            <button
              on:click={() => signIn('google')}
              class="btn-soft w-full flex items-center justify-center gap-3 group"
            >
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Continue with Google</span>
            </button>

            <p class="text-xs text-soft">
              By signing in, you agree to our terms of service and privacy policy
            </p>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

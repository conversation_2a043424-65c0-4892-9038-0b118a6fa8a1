<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import ToastContainer from '$lib/components/ToastContainer.svelte';

	let mounted = false;

	onMount(async () => {
		mounted = true;
		
		// Commented out anime.js animations
		/*
		const anime = ((await import('animejs')) as any).default || (await import('animejs'));
		
		const tl = anime.timeline({
			easing: 'easeInOutSine',
			loop: true,
			direction: 'alternate'
		});

		tl.add({
			targets: '.float-element',
			translateY: [-20, 20],
			duration: 4000,
			delay: anime.stagger(300)
		});
		*/
	});
</script>

<div class="min-h-screen bg-background relative overflow-hidden">
	<!-- Soft floating background elements -->
	<div class="absolute inset-0 overflow-hidden pointer-events-none">
		<div class="floating-element top-20 left-10 w-96 h-96 bg-muted/30 rounded-full blur-3xl"></div>
		<div class="floating-element bottom-20 right-20 w-80 h-80 bg-accent/20 rounded-full blur-3xl"></div>
		<div class="floating-element top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-secondary/40 rounded-full blur-3xl"></div>
		<div class="floating-element top-10 right-10 w-48 h-48 bg-primary/10 rounded-full blur-2xl"></div>
		<div class="floating-element bottom-10 left-20 w-72 h-72 bg-muted/20 rounded-full blur-3xl"></div>
	</div>

	<!-- Main content -->
	<main class="relative z-10 container-soft">
		{#if mounted}
			<div in:fade={{ duration: 500, easing: (t) => t * t * (3 - 2 * t) }}>
				<slot />
			</div>
		{/if}
	</main>
</div>

<!-- Toast notifications -->
<ToastContainer />

<style>
	:global(body) {
		overflow-x: hidden;
	}
</style>

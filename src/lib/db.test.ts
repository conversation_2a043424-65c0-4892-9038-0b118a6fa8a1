import { describe, it, expect } from 'vitest';
import { generateId } from './db';

describe('Database Operations', () => {
	describe('generateId', () => {
		it('should generate valid UUID v7', () => {
			const id = generateId();
			expect(id).toMatch(
				/^[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
			);
		});

		it('should generate unique IDs', () => {
			const id1 = generateId();
			const id2 = generateId();
			expect(id1).not.toBe(id2);
		});
	});

	describe('Database Functions', () => {
		it('should have required database functions', () => {
			// Test that the functions exist
			expect(typeof generateId).toBe('function');
		});
	});
});

import { QDRANT_KEY, QDRANT_URL } from '$env/static/private';
import { QdrantClient } from '@qdrant/js-client-rest';
import { v7 as uuidv7 } from 'uuid';
import { collection } from './constants';


// Qdrant client configuration
export const qdrant = new QdrantClient({
	url: QDRANT_URL || 'http://localhost:6333',
	apiKey: QDRANT_KEY
});


// Utility functions
export function generateId(): string {
	return uuidv7();
}

// Initialize collections
export async function initializeCollections() {
	try {
		await qdrant.getCollection(collection);
	} catch (error) {
		// Collection doesn't exist, create it
		await qdrant.createCollection(collection, {
			vectors: {
				size: 768,
				distance: 'Dot'
			}
		});
	}
}

// Database operations wrapper
export async function upsertPoint<T extends { id?: string; s: string }>(
	data: T
) {
	const id = data.id || generateId();

	const vector = new Array(768).fill(0);

	await qdrant.upsert(collection, {
		points: [
			{
				id,
				payload: { ...data, id },
				vector
			}
		]
	});

	return { ...data, id };
}

export async function searchByPayload<T>(
	filters: Record<string, any>,
	limit: number = 144
): Promise<T[]> {
	const mustFilters = Object.entries(filters)
		.filter(([, value]) => value !== undefined && value !== null && value !== '')
		.map(([key, value]) => ({
			key,
			match: { value }
		}));

	// If no valid filters, return empty array
	if (mustFilters.length === 0) {
		return [];
	}

	try {
		const results = await qdrant.scroll(collection, {
			filter: {
				must: mustFilters
			},
			limit,
			with_payload: true,
			with_vector: false
		});

		// console.debug('searchByPayload results', results);

		return results.points.map((point) => point.payload as T);
	} catch (error) {
		console.error('Error in searchByPayload:', error);
		console.error('Filters:', filters);
		console.error('Must filters:', mustFilters);
		throw error;
	}
}

export async function searchByName<T>(
	collection: string,
	name: string,
	tenantId: string,
	limit: number = 3
): Promise<T[]> {
	// For now, we'll do a simple scroll with name filtering
	// In production, you'd want to use proper text search
	const results = await qdrant.scroll(collection, {
		filter: {
			must: [{ key: 's', match: { value: tenantId } }]
		},
		limit: 100,
		with_payload: true,
		with_vector: false
	});

	// Client-side name filtering (in production, use proper text search)
	const filtered = results.points
		.filter((point) =>
			(
				(point.payload as Record<string, any>).n?.toLowerCase() || ''
			).includes(name.toLowerCase())
		)
		.slice(0, limit);

	return filtered.map((point) => point.payload as T);
}

export async function getById<T>(
	id: string
): Promise<T | null> {
	try {
		const result = await qdrant.retrieve(collection, {
			ids: [id],
			with_payload: true,
			with_vector: false
		});
		
		if (result.length > 0) {
			return result[0].payload as T;
		}
		return null;
	} catch {
		return null;
	}
}

export async function deleteById(
	collection: string,
	id: string
): Promise<void> {
	await qdrant.delete(collection, {
		points: [id]
	});
}

export async function updatePoint<T extends { id: string; s: string }>(
	id: string,
	data: Partial<T>
): Promise<void> {
	const existing = await getById<T>(id);
	if (!existing) {
		throw new Error('Document not found');
	}

	await upsertPoint({ ...existing, ...data, id });
}

// Get user's name from their ID
export async function getUserNameFromId(
	userId: string
): Promise<string> {
	const user = await getById(userId);

	if (user && (user as any).n) {
		return (user as any).n;
	}

	// If user not found, return Unknown User
	return 'Unknown User';
}

// Presence-specific database functions
export async function getLastPresenceRecord(
	userId: string,
	schoolId: string
): Promise<any | null> {
	const records = await searchByPayload({
		s: 'p',
		u: userId,
		sc: schoolId
	}, 100);

	if (records.length === 0) return null;

	// Sort by timestamp descending to get the most recent
	return records.sort((a: any, b: any) => b.t - a.t)[0];
}

export async function getUserPresenceRecords(
	userId: string,
	schoolId: string,
	limit: number = 100
): Promise<any[]> {
	const records = await searchByPayload({
		s: 'p',
		u: userId,
		sc: schoolId
	}, limit);

	// Sort by timestamp descending
	return records.sort((a: any, b: any) => b.t - a.t);
}

export async function validatePresenceRecord(
	userId: string,
	schoolId: string,
	direction: 1 | 0
): Promise<{ valid: boolean; reason?: string }> {
	// Check if user exists and belongs to the school
	const schoolUser = await getById(userId);
	if (!schoolUser || (schoolUser as any).s !== 'sch_usr') {
		return { valid: false, reason: 'Invalid user' };
	}

	if ((schoolUser as any).sc !== schoolId) {
		return { valid: false, reason: 'User does not belong to this school' };
	}

	// Check last record to prevent duplicate consecutive records
	const lastRecord = await getLastPresenceRecord(userId, schoolId);
	if (lastRecord && lastRecord.d === direction) {
		const actionType = direction === 1 ? 'sign in' : 'sign out';
		return {
			valid: false,
			reason: `Cannot ${actionType} - already ${direction === 1 ? 'signed in' : 'signed out'}`
		};
	}

	return { valid: true };
}

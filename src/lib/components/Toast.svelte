<script lang="ts">
  import { onMount } from 'svelte';
  import { fade, fly } from 'svelte/transition';

  export let type: 'success' | 'error' | 'warning' | 'info' = 'info';
  export let message: string;
  export let duration: number = 5000;
  export let dismissible: boolean = true;
  export let onDismiss: (() => void) | undefined = undefined;

  let visible = true;
  let timeoutId: number;

  onMount(() => {
    if (duration > 0) {
      timeoutId = setTimeout(() => {
        dismiss();
      }, duration);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  });

  function dismiss() {
    visible = false;
    if (onDismiss) {
      setTimeout(onDismiss, 300); // Wait for animation to complete
    }
  }

  function getIcon() {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
      default:
        return 'ℹ';
    }
  }

  function getTypeClass() {
    switch (type) {
      case 'success':
        return 'toast-success';
      case 'error':
        return 'toast-error';
      case 'warning':
        return 'toast-warning';
      case 'info':
      default:
        return 'toast-info';
    }
  }
</script>

{#if visible}
  <div
    class="toast {getTypeClass()}"
    transition:fly={{ y: -20, duration: 300 }}
    role="alert"
    aria-live="polite"
  >
    <div class="toast-content">
      <div class="toast-icon">
        {getIcon()}
      </div>
      <div class="toast-message">
        {message}
      </div>
      {#if dismissible}
        <button
          class="toast-dismiss"
          on:click={dismiss}
          aria-label="Dismiss notification"
        >
          ✕
        </button>
      {/if}
    </div>
  </div>
{/if}

<style>
  .toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    min-width: 300px;
    max-width: 500px;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
  }

  .toast-icon {
    flex-shrink: 0;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: bold;
    font-size: 0.9rem;
  }

  .toast-message {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .toast-dismiss {
    flex-shrink: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .toast-dismiss:hover {
    opacity: 1;
  }

  /* Success Toast */
  .toast-success {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
    color: #166534;
  }

  .toast-success .toast-icon {
    background: rgba(34, 197, 94, 0.2);
    color: #166534;
  }

  .toast-success .toast-dismiss {
    color: #166534;
  }

  /* Error Toast */
  .toast-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #991b1b;
  }

  .toast-error .toast-icon {
    background: rgba(239, 68, 68, 0.2);
    color: #991b1b;
  }

  .toast-error .toast-dismiss {
    color: #991b1b;
  }

  /* Warning Toast */
  .toast-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
    color: #92400e;
  }

  .toast-warning .toast-icon {
    background: rgba(245, 158, 11, 0.2);
    color: #92400e;
  }

  .toast-warning .toast-dismiss {
    color: #92400e;
  }

  /* Info Toast */
  .toast-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    color: #1e40af;
  }

  .toast-info .toast-icon {
    background: rgba(59, 130, 246, 0.2);
    color: #1e40af;
  }

  .toast-info .toast-dismiss {
    color: #1e40af;
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .toast {
      backdrop-filter: blur(10px);
      border-color: rgba(255, 255, 255, 0.1);
    }

    .toast-success {
      background: rgba(34, 197, 94, 0.15);
      color: #4ade80;
    }

    .toast-success .toast-icon {
      background: rgba(34, 197, 94, 0.3);
      color: #4ade80;
    }

    .toast-success .toast-dismiss {
      color: #4ade80;
    }

    .toast-error {
      background: rgba(239, 68, 68, 0.15);
      color: #f87171;
    }

    .toast-error .toast-icon {
      background: rgba(239, 68, 68, 0.3);
      color: #f87171;
    }

    .toast-error .toast-dismiss {
      color: #f87171;
    }

    .toast-warning {
      background: rgba(245, 158, 11, 0.15);
      color: #fbbf24;
    }

    .toast-warning .toast-icon {
      background: rgba(245, 158, 11, 0.3);
      color: #fbbf24;
    }

    .toast-warning .toast-dismiss {
      color: #fbbf24;
    }

    .toast-info {
      background: rgba(59, 130, 246, 0.15);
      color: #60a5fa;
    }

    .toast-info .toast-icon {
      background: rgba(59, 130, 246, 0.3);
      color: #60a5fa;
    }

    .toast-info .toast-dismiss {
      color: #60a5fa;
    }
  }

  /* Mobile responsiveness */
  @media (max-width: 640px) {
    .toast {
      left: 1rem;
      right: 1rem;
      min-width: auto;
    }
  }
</style>

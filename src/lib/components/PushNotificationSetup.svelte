<script lang="ts">
  import { onMount } from 'svelte';
  import { pushNotificationManager } from '$lib/push-notifications';
  import { toast } from '$lib/stores/toast';

  export let userId: string;
  export let schoolId: string;

  let isSupported = false;
  let isSubscribed = false;
  let isLoading = false;
  let permission: NotificationPermission = 'default';

  onMount(async () => {
    // Check if push notifications are supported
    isSupported = 'serviceWorker' in navigator && 'PushManager' in window;
    
    if (isSupported) {
      permission = Notification.permission;
      
      // Initialize push notification manager
      const initialized = await pushNotificationManager.initialize();
      if (initialized) {
        isSubscribed = await pushNotificationManager.isSubscribed(userId, schoolId);
      }
    }
  });

  async function enableNotifications() {
    if (!isSupported) {
      toast.error('Push notifications are not supported on this device');
      return;
    }

    isLoading = true;

    try {
      // Request permission
      permission = await pushNotificationManager.requestPermission();
      
      if (permission === 'granted') {
        // Subscribe to push notifications
        const subscription = await pushNotificationManager.subscribe(userId, schoolId);
        
        if (subscription) {
          isSubscribed = true;
          toast.success('Push notifications enabled successfully!');
        } else {
          toast.error('Failed to enable push notifications');
        }
      } else if (permission === 'denied') {
        toast.error('Push notifications were denied. Please enable them in your browser settings.');
      } else {
        toast.warning('Push notification permission was not granted');
      }
    } catch (error) {
      console.error('Error enabling push notifications:', error);
      toast.error('Failed to enable push notifications');
    } finally {
      isLoading = false;
    }
  }

  async function disableNotifications() {
    isLoading = true;

    try {
      const success = await pushNotificationManager.unsubscribe(userId, schoolId);
      
      if (success) {
        isSubscribed = false;
        toast.success('Push notifications disabled');
      } else {
        toast.error('Failed to disable push notifications');
      }
    } catch (error) {
      console.error('Error disabling push notifications:', error);
      toast.error('Failed to disable push notifications');
    } finally {
      isLoading = false;
    }
  }

  function getStatusText(): string {
    if (!isSupported) return 'Not supported';
    if (permission === 'denied') return 'Blocked';
    if (isSubscribed) return 'Enabled';
    return 'Disabled';
  }

  function getStatusClass(): string {
    if (!isSupported || permission === 'denied') return 'status-error';
    if (isSubscribed) return 'status-success';
    return 'status-warning';
  }
</script>

<div class="push-notification-setup">
  <div class="setup-header">
    <div class="setup-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
      </svg>
    </div>
    <div class="setup-content">
      <h3>Push Notifications</h3>
      <p>Get notified when your attendance is recorded</p>
    </div>
    <div class="setup-status">
      <span class="status-badge {getStatusClass()}">
        {getStatusText()}
      </span>
    </div>
  </div>

  {#if isSupported}
    <div class="setup-body">
      {#if permission === 'denied'}
        <div class="permission-denied">
          <p>Push notifications are blocked. To enable them:</p>
          <ol>
            <li>Click the lock icon in your browser's address bar</li>
            <li>Change notifications from "Block" to "Allow"</li>
            <li>Refresh this page</li>
          </ol>
        </div>
      {:else if !isSubscribed}
        <div class="enable-section">
          <div class="benefits">
            <h4>Why enable push notifications?</h4>
            <ul>
              <li>Get instant confirmation when you sign in or out</li>
              <li>Receive attendance reminders and updates</li>
              <li>Stay informed about school attendance policies</li>
              <li>Never miss important attendance notifications</li>
            </ul>
          </div>
          
          <button 
            class="enable-btn"
            on:click={enableNotifications}
            disabled={isLoading}
          >
            {#if isLoading}
              <div class="spinner"></div>
              Enabling...
            {:else}
              Enable Push Notifications
            {/if}
          </button>
        </div>
      {:else}
        <div class="enabled-section">
          <div class="success-message">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            <span>Push notifications are enabled!</span>
          </div>
          
          <p class="enabled-description">
            You'll receive notifications when your attendance is recorded.
          </p>
          
          <button 
            class="disable-btn"
            on:click={disableNotifications}
            disabled={isLoading}
          >
            {#if isLoading}
              <div class="spinner"></div>
              Disabling...
            {:else}
              Disable Notifications
            {/if}
          </button>
        </div>
      {/if}
    </div>
  {:else}
    <div class="not-supported">
      <p>Push notifications are not supported on this device or browser.</p>
      <p>Try using a modern browser like Chrome, Firefox, or Safari.</p>
    </div>
  {/if}
</div>

<style>
  .push-notification-setup {
    background: white;
    border: 1px solid var(--color-border);
    border-radius: 0.75rem;
    overflow: hidden;
  }

  .setup-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-border);
  }

  .setup-icon {
    width: 40px;
    height: 40px;
    background: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .setup-content {
    flex: 1;
  }

  .setup-content h3 {
    font-size: 1.1rem;
    margin: 0 0 0.25rem 0;
    color: var(--color-foreground);
  }

  .setup-content p {
    font-size: 0.9rem;
    color: var(--color-muted-foreground);
    margin: 0;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .status-success {
    background: #dcfce7;
    color: #166534;
  }

  .status-warning {
    background: #fef3c7;
    color: #92400e;
  }

  .status-error {
    background: #fee2e2;
    color: #991b1b;
  }

  .setup-body {
    padding: 1.5rem;
  }

  .benefits {
    margin-bottom: 1.5rem;
  }

  .benefits h4 {
    font-size: 1rem;
    margin: 0 0 0.75rem 0;
    color: var(--color-foreground);
  }

  .benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .benefits li {
    padding: 0.5rem 0;
    color: var(--color-muted-foreground);
    position: relative;
    padding-left: 1.5rem;
    font-size: 0.9rem;
  }

  .benefits li::before {
    content: '✓';
    color: var(--color-primary);
    position: absolute;
    left: 0;
    font-weight: bold;
  }

  .enable-btn {
    background: var(--color-primary);
    color: var(--color-primary-foreground);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
  }

  .enable-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .enable-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .success-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #166534;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .enabled-description {
    color: var(--color-muted-foreground);
    margin: 0 0 1.5rem 0;
    font-size: 0.9rem;
  }

  .disable-btn {
    background: var(--color-secondary);
    color: var(--color-secondary-foreground);
    border: 1px solid var(--color-border);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .disable-btn:hover:not(:disabled) {
    background: var(--color-muted);
  }

  .disable-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .permission-denied {
    color: var(--color-muted-foreground);
  }

  .permission-denied p {
    margin: 0 0 1rem 0;
  }

  .permission-denied ol {
    margin: 0;
    padding-left: 1.5rem;
  }

  .permission-denied li {
    margin: 0.5rem 0;
    font-size: 0.9rem;
  }

  .not-supported {
    color: var(--color-muted-foreground);
    text-align: center;
  }

  .not-supported p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @media (max-width: 640px) {
    .setup-header {
      padding: 1rem;
    }

    .setup-body {
      padding: 1rem;
    }
  }
</style>

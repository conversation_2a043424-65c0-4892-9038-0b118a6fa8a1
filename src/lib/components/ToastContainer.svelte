<script lang="ts">
  import { toast } from '$lib/stores/toast';
  import Toast from './Toast.svelte';
</script>

<div class="toast-container">
  {#each $toast as toastMessage (toastMessage.id)}
    <Toast
      type={toastMessage.type}
      message={toastMessage.message}
      duration={toastMessage.duration}
      dismissible={toastMessage.dismissible}
      onDismiss={() => toast.remove(toastMessage.id)}
    />
  {/each}
</div>

<style>
  .toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    pointer-events: none;
  }

  .toast-container :global(.toast) {
    pointer-events: auto;
  }

  @media (max-width: 640px) {
    .toast-container {
      left: 1rem;
      right: 1rem;
    }
  }
</style>

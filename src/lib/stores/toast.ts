import { writable } from 'svelte/store';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  dismissible?: boolean;
}

function createToastStore() {
  const { subscribe, update } = writable<ToastMessage[]>([]);

  const store = {
    subscribe,

    add: (toast: Omit<ToastMessage, 'id'>) => {
      const id = Math.random().toString(36).substr(2, 9);
      const newToast: ToastMessage = {
        id,
        duration: 5000,
        dismissible: true,
        ...toast
      };

      update(toasts => [...toasts, newToast]);

      // Auto-remove after duration
      if (newToast.duration && newToast.duration > 0) {
        setTimeout(() => {
          update(toasts => toasts.filter(t => t.id !== id));
        }, newToast.duration);
      }

      return id;
    },

    remove: (id: string) => {
      update(toasts => toasts.filter(t => t.id !== id));
    },

    clear: () => {
      update(() => []);
    },

    // Convenience methods
    success: (message: string, options?: Partial<Omit<ToastMessage, 'id' | 'type' | 'message'>>) => {
      return store.add({ type: 'success', message, ...options });
    },

    error: (message: string, options?: Partial<Omit<ToastMessage, 'id' | 'type' | 'message'>>) => {
      return store.add({ type: 'error', message, ...options });
    },

    warning: (message: string, options?: Partial<Omit<ToastMessage, 'id' | 'type' | 'message'>>) => {
      return store.add({ type: 'warning', message, ...options });
    },

    info: (message: string, options?: Partial<Omit<ToastMessage, 'id' | 'type' | 'message'>>) => {
      return store.add({ type: 'info', message, ...options });
    }
  };

  return store;
}

export const toast = createToastStore();

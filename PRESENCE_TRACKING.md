# Presence Tracking System

A comprehensive attendance tracking system for schools with QR code scanning, real-time notifications, and detailed reporting.

## Features

### ✅ QR Code Scanning
- **Camera-based scanning** using device camera
- **Manual input fallback** for accessibility
- **Real-time validation** of QR codes
- **User-friendly interface** with visual feedback

### ✅ Attendance Management
- **Sign in/out tracking** with timestamps
- **Duplicate prevention** - prevents consecutive identical actions
- **School-specific validation** - ensures users belong to the correct school
- **Real-time status updates** showing current presence state

### ✅ Push Notifications
- **Instant notifications** when attendance is recorded
- **Service worker integration** for reliable delivery
- **User preference management** - enable/disable per school
- **Concise messaging** optimized for mobile notifications

### ✅ User Experience
- **Beautiful, calm design** following Apple design principles
- **Responsive layout** works on all devices
- **Smooth animations** using anime.js
- **Accessible interface** with proper ARIA labels

### ✅ Data Management
- **Qdrant vector database** for scalable storage
- **Multitenancy support** with tenant isolation
- **UUID v7 identifiers** for temporal ordering
- **Efficient querying** with optimized filters

## System Architecture

### Database Schema

```typescript
// Presence Records
interface PresenceRecord {
  s: 'p';           // tenant id
  d: 1 | 0;         // direction: 1 = sign in, 0 = sign out
  t: number;        // timestamp (Unix)
  u: string;        // user uuid (SchoolUser.id)
  sc: string;       // school uuid
  id?: string;
}

// Notification Subscriptions
interface NotificationSubscription {
  s: 'n';           // tenant id
  u: string;        // user uuid
  e: string;        // endpoint
  k: string;        // p256dh key
  a: string;        // auth token
  sc: string;       // school uuid
  ac: boolean;      // active status
  id?: string;
}
```

### API Endpoints

- **POST /api/push** - Send push notifications
- **GET /api/push** - Get VAPID public key

### Routes

#### Presence Management
- `/sc/:school/p` - School presence dashboard
- `/sc/:school/p/i` - Sign in page with QR scanning
- `/sc/:school/p/o` - Sign out page with QR scanning

#### User Management
- `/u/:user/qr` - Generate user QR code
- `/u/:user/s` - User settings (notifications)
- `/u/:user/:school/p` - User presence records

## Usage Guide

### For School Administrators

1. **Access Presence Dashboard**
   ```
   Navigate to /sc/{school-id}/p
   ```

2. **Start Attendance Scanning**
   - Click "Sign Users In" for arrivals
   - Click "Sign Users Out" for departures

3. **Monitor Statistics**
   - View real-time attendance counts
   - Track currently present users
   - Monitor daily sign-in/out activity

### For Students/Staff

1. **Get Your QR Code**
   ```
   Navigate to /u/{user-id}/qr
   ```

2. **Enable Notifications**
   ```
   Navigate to /u/{user-id}/s
   Click "Enable Push Notifications"
   ```

3. **View Attendance History**
   ```
   Navigate to /u/{user-id}/{school-id}/p
   ```

### QR Code Scanning Process

1. **Validation Chain**
   - QR code contains user UUID
   - System validates user exists (`s: 'sch_usr'`)
   - Checks user belongs to correct school
   - Prevents duplicate consecutive actions

2. **Error Handling**
   - Invalid QR codes show clear error messages
   - Network issues display retry options
   - Permission errors provide guidance

3. **Success Flow**
   - Attendance record created with timestamp
   - Push notification sent to user
   - Success message displayed
   - Scanner ready for next code

## Technical Implementation

### QR Code Generation
```typescript
import QRCode from 'qrcode';

await QRCode.toCanvas(canvas, userId, {
  width: 300,
  margin: 2,
  color: {
    dark: '#000000',
    light: '#FFFFFF'
  }
});
```

### Push Notifications
```typescript
// Subscribe to notifications
const subscription = await registration.pushManager.subscribe({
  userVisibleOnly: true,
  applicationServerKey: VAPID_PUBLIC_KEY
});

// Send notification
await fetch('/api/push', {
  method: 'POST',
  body: JSON.stringify({
    userId,
    schoolId,
    message: `You signed in at ${time}`,
    title: 'Attendance Recorded'
  })
});
```

### Validation Logic
```typescript
export async function validatePresenceRecord(
  userId: string,
  schoolId: string,
  direction: 1 | 0
): Promise<{ valid: boolean; reason?: string }> {
  // Check user exists and belongs to school
  const schoolUser = await getById(userId);
  if (!schoolUser || schoolUser.s !== 'sch_usr') {
    return { valid: false, reason: 'Invalid user' };
  }

  if (schoolUser.sc !== schoolId) {
    return { valid: false, reason: 'User does not belong to this school' };
  }

  // Prevent duplicate consecutive records
  const lastRecord = await getLastPresenceRecord(userId, schoolId);
  if (lastRecord && lastRecord.d === direction) {
    const actionType = direction === 1 ? 'sign in' : 'sign out';
    return { 
      valid: false, 
      reason: `Cannot ${actionType} - already ${direction === 1 ? 'signed in' : 'signed out'}` 
    };
  }

  return { valid: true };
}
```

## Security Considerations

- **QR Code Validation** - All codes validated against database
- **School Isolation** - Users can only access their assigned schools
- **Secure Storage** - All data encrypted in transit and at rest
- **Permission Checks** - Proper authorization for all operations

## Performance Optimizations

- **Efficient Queries** - Optimized database filters
- **Caching Strategy** - Smart caching of user data
- **Lazy Loading** - Components loaded on demand
- **Service Worker** - Offline capability and fast loading

## Browser Support

- **Modern Browsers** - Chrome 60+, Firefox 55+, Safari 11+
- **Mobile Support** - iOS Safari, Chrome Mobile
- **PWA Features** - Service worker, push notifications
- **Camera Access** - getUserMedia API for QR scanning

## Testing

Run the test suite:
```bash
# Unit tests
npm run test:unit

# E2E tests
npm run test:e2e

# All tests
npm run test
```

## Deployment

1. **Environment Variables**
   ```
   QDRANT_URL=http://localhost:6333
   QDRANT_KEY=your-api-key
   ```

2. **Build and Deploy**
   ```bash
   npm run build
   npm run preview
   ```

3. **Service Worker Registration**
   - Ensure `/sw.js` is accessible
   - Configure VAPID keys for push notifications
   - Set up proper HTTPS for production

## Future Enhancements

- **Geofencing** - Location-based attendance validation
- **Facial Recognition** - Alternative to QR codes
- **Analytics Dashboard** - Advanced reporting and insights
- **Integration APIs** - Connect with existing school systems
- **Bulk Operations** - Mass sign-in/out capabilities

import { test, expect } from '@playwright/test';

test.describe('Presence Tracking System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
  });

  test('should display presence dashboard for a school', async ({ page }) => {
    // Navigate to a school presence dashboard
    await page.goto('/sc/test-school-id/p');
    
    // Check if the page loads correctly
    await expect(page.locator('h1')).toContainText('Presence Management');
    
    // Check for sign in and sign out buttons
    await expect(page.locator('text=Sign Users In')).toBeVisible();
    await expect(page.locator('text=Sign Users Out')).toBeVisible();
    
    // Check for stats section
    await expect(page.locator('text=Today\'s Overview')).toBeVisible();
  });

  test('should navigate to sign in page', async ({ page }) => {
    await page.goto('/sc/test-school-id/p');
    
    // Click on "Start Sign In" button
    await page.click('text=Start Sign In');
    
    // Should navigate to sign in page
    await expect(page).toHaveURL('/sc/test-school-id/p/i');
    await expect(page.locator('h1')).toContainText('Sign In');
  });

  test('should navigate to sign out page', async ({ page }) => {
    await page.goto('/sc/test-school-id/p');
    
    // Click on "Start Sign Out" button
    await page.click('text=Start Sign Out');
    
    // Should navigate to sign out page
    await expect(page).toHaveURL('/sc/test-school-id/p/o');
    await expect(page.locator('h1')).toContainText('Sign Out');
  });

  test('should display QR scanner interface on sign in page', async ({ page }) => {
    await page.goto('/sc/test-school-id/p/i');
    
    // Check for scanner interface elements
    await expect(page.locator('text=Ready to Scan')).toBeVisible();
    await expect(page.locator('text=Start Scanning')).toBeVisible();
    
    // Check for manual input option
    await expect(page.locator('text=Enter manually')).toBeVisible();
  });

  test('should show QR code generation page', async ({ page }) => {
    await page.goto('/u/test-user-id/qr');
    
    // Check if QR code page loads
    await expect(page.locator('h1')).toContainText('Your QR Code');
    
    // Check for action buttons
    await expect(page.locator('text=Copy')).toBeVisible();
    await expect(page.locator('text=Download')).toBeVisible();
  });

  test('should display user settings page', async ({ page }) => {
    await page.goto('/u/test-user-id/s');
    
    // Check if settings page loads
    await expect(page.locator('h1')).toContainText('Settings');
    
    // Check for notifications section
    await expect(page.locator('text=Push Notifications')).toBeVisible();
    
    // Check for account section
    await expect(page.locator('text=Account')).toBeVisible();
  });

  test('should display user presence records', async ({ page }) => {
    await page.goto('/u/test-user-id/test-school-id/p');
    
    // Check if presence records page loads
    await expect(page.locator('h1')).toContainText('Presence Records');
    
    // Check for current status section
    await expect(page.locator('text=Current Status')).toBeVisible();
    
    // Check for filters
    await expect(page.locator('text=Filter Records')).toBeVisible();
    await expect(page.locator('select')).toBeVisible();
  });

  test('should handle back navigation correctly', async ({ page }) => {
    // Start from presence dashboard
    await page.goto('/sc/test-school-id/p');
    
    // Navigate to sign in page
    await page.click('text=Start Sign In');
    await expect(page).toHaveURL('/sc/test-school-id/p/i');
    
    // Click back button
    await page.click('text=← Back');
    await expect(page).toHaveURL('/sc/test-school-id/p');
  });

  test('should show appropriate error messages for invalid QR codes', async ({ page }) => {
    await page.goto('/sc/test-school-id/p/i');
    
    // Start scanning
    await page.click('text=Start Scanning');
    
    // Look for manual input option
    await page.click('text=Enter manually');
    
    // Enter invalid QR code data
    await page.fill('input[placeholder*="Enter QR code"]', 'invalid-qr-code');
    await page.click('text=Submit');
    
    // Should show error message (this would require proper error handling)
    // In a real test, we'd check for toast notifications or error messages
  });

  test('should display responsive design on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/sc/test-school-id/p');
    
    // Check if mobile layout is applied
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('text=Sign Users In')).toBeVisible();
    await expect(page.locator('text=Sign Users Out')).toBeVisible();
  });
});

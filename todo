# Attendance System TODO

## Security & Validation
- [ ] Add QR code expiration time
- [ ] Implement rate limiting on scan attempts
- [ ] Prevent QR code screenshot reuse
- [ ] Add validation: Can't sign in/out for future dates
- [ ] Add validation: Can't sign in/out more than X hours ago (prevent backdating)
- [ ] Ensure QR code belongs to valid school user
- [ ] Verify user authorization for specific school

## Error Handling & User Experience
- [ ] Add confirmation before creating attendance record
- [ ] Show current status (am I signed in or out?) on scan pages
- [ ] Add attendance history preview on scan pages
- [ ] Implement camera permission denied handling
- [ ] Handle QR code scan timeout/failure scenarios
- [ ] Add network connectivity issue handling
- [ ] Handle invalid QR codes (wrong format, expired, etc.)
- [ ] Implement database write failure handling
- [ ] Add push notification send failure handling

## Accessibility
- [ ] Add screen reader announcements for scan results
- [ ] Implement keyboard navigation alternatives to QR scanning
- [ ] Add high contrast mode support for QR display
- [ ] Implement voice feedback for successful scans
- [ ] Create manual ID entry fallback for camera issues

## Additional Features & Pages
- [ ] Create admin dashboard to view all attendance
- [ ] Add ability to manually correct attendance records
- [ ] Implement bulk operations (mass sign-in for events)
- [ ] Create attendance reports/analytics
- [ ] Add real-time updates (WebSocket/SSE)
- [ ] Plan offline capability and data synchronization

## Data Structure Enhancements
- [ ] Add location tracking (which building/room)
- [ ] Implement attendance categories (regular, late, early departure)
- [ ] Create system to handle partial days or breaks
- [ ] Plan for system clock differences handling
- [ ] Implement grace periods for late sign-ins

## Technical Architecture
- [ ] Set up service worker registration (/sw.js)
- [ ] Generate and configure VAPID keys
- [ ] Create subscription object storage (endpoint, keys, auth)
- [ ] Implement server endpoint /api/push for notifications
- [ ] Plan offline capability strategy
- [ ] Design data synchronization approach

## Business Logic
- [ ] Define handling for overlapping sign-in/out times
- [ ] Create system clock difference handling
- [ ] Implement grace periods for late sign-ins
- [ ] Define partial day attendance rules
